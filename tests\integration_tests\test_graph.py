import pytest
from langchain_core.messages import HumanMessage

from agent import graph

pytestmark = pytest.mark.anyio


@pytest.mark.langsmith
async def test_chatbot_response() -> None:
    inputs = {"messages": [HumanMessage(content="Hello, how are you?")]}
    config = {
        "configurable": {
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        }
    }
    res = await graph.ainvoke(inputs, config)
    assert res is not None
    assert "messages" in res
    assert len(res["messages"]) > 0

