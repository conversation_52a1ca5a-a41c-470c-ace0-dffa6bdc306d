


from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from typing import Any, Dict

from agent.state import State
from shared import create_llm


async def call_model(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Generate AI response using the configured LLM."""
    configuration = config.get("configurable", {})
    
    # Get model configuration
    model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
    system_prompt = configuration.get("system_prompt", "You are a helpful assistant.")
    
    # Create LLM instance
    llm = create_llm(model_name)
    
    # Prepare messages with system prompt
    messages = [HumanMessage(content=system_prompt)] + state["messages"]
    
    # Generate response
    response = await llm.ainvoke(messages)
    
    return {"messages": [response]}
