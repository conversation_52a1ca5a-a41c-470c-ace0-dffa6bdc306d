"""LangGraph chatbot graph.

A simple chatbot that uses OpenRouter LLM via llm_factory.
"""

from __future__ import annotations

from dataclasses import dataclass

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from typing import Any, Dict, TypedDict, Annotated, Sequence

from langgraph.graph import StateGraph, add_messages


from agent.state import State
from agent.nodes.call_model import call_model


class Configuration(TypedDict):
    """Configurable parameters for the chatbot.

    Set these when creating assistants OR when invoking the graph.
    """
    
    model_name: str
    system_prompt: str



# Define the graph
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_edge("__start__", "call_model")
    .compile(name="Chatbot")
)

